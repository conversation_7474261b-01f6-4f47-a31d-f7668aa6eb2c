import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Expense, Roommate } from '@/types';
import { formatCurrency } from '@/utils/calculations';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { markExpenseAsPaid } from '@/services/expenses';

interface ActivityFeedProps {
  expenses: Expense[];
  roommates: Roommate[];
  currency: string;
  onExpenseUpdated?: () => void;
}

export const ActivityFeed = ({ expenses, roommates, currency, onExpenseUpdated }: ActivityFeedProps) => {
  const { user } = useAuth();
  const { toast } = useToast();

  const getRoommateName = (id: string) =>
    roommates.find(r => r.id === id)?.name || 'Unknown';

  const formatDate = (date: Date) => {
    const d = new Date(date);
    return d.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleMarkAsPaid = async (expenseId: string, isPaid: boolean) => {
    try {
      await markExpenseAsPaid(expenseId, isPaid);
      toast({
        title: isPaid ? "✅ Expense marked as paid" : "⏳ Expense marked as unpaid",
        description: isPaid ? "All splits have been completed." : "Expense is now pending payment.",
      });
      onExpenseUpdated?.();
    } catch (error: any) {
      toast({
        title: "Failed to update expense",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  return (
    <Card className="shadow-card">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          📋 Recent Activity
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {expenses.length === 0 ? (
          <p className="text-muted-foreground text-center py-4">
            No expenses yet. Add your first expense to get started!
          </p>
        ) : (
          expenses
            .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
            .map((expense) => (
              <div key={expense.id} className={`border-l-4 ${expense.isPaid ? 'border-green-500' : 'border-primary'} pl-4 py-2`}>
                <div className="flex justify-between items-start mb-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-sm">
                      💳 {formatCurrency(expense.amount, currency)} – {expense.description}
                    </span>
                    <Badge
                      variant={expense.isPaid ? "secondary" : "outline"}
                      className={`text-xs ${expense.isPaid ? 'bg-green-100 text-green-800' : ''}`}
                    >
                      {expense.isPaid ? '✅ Paid' : '⏳ Pending'}
                    </Badge>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {formatDate(expense.date)}
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground">
                  Paid by <span className="font-medium">{getRoommateName(expense.payerId)}</span>,
                  split {expense.participants.length} way{expense.participants.length > 1 ? 's' : ''}
                </p>
                <div className="flex justify-between items-center mt-1">
                  <p className="text-xs text-muted-foreground">
                    Logged by <span className="font-medium">{expense.loggedBy}</span> • {formatDate(expense.loggedAt)}
                    {expense.isPaid && expense.paidBy && (
                      <span> • Marked paid by <span className="font-medium">{expense.paidBy}</span></span>
                    )}
                  </p>
                  <Button
                    size="sm"
                    variant={expense.isPaid ? "outline" : "default"}
                    onClick={() => handleMarkAsPaid(expense.id, !expense.isPaid)}
                    className="text-xs h-6 px-2"
                  >
                    {expense.isPaid ? 'Mark Unpaid' : 'Mark Paid'}
                  </Button>
                </div>
              </div>
            ))
        )}
      </CardContent>
    </Card>
  );
};
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useApi, useMutation } from '@/hooks/useApi';
import { getRoommates } from '@/services/roommates';
import { createExpense } from '@/services/expenses';
import { ExpenseParticipant } from '@/types';

interface ExpenseFormProps {
  onClose: () => void;
  currentUser: string;
}

export const ExpenseForm = ({ onClose, currentUser }: ExpenseFormProps) => {
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [descriptionType, setDescriptionType] = useState('');
  const [customDescription, setCustomDescription] = useState('');
  const [payerId, setPayerId] = useState('');
  const [selectedParticipants, setSelectedParticipants] = useState<string[]>([]);
  const [splitType, setSplitType] = useState<'equal' | 'custom'>('equal');
  const [customAmounts, setCustomAmounts] = useState<Record<string, string>>({});

  const { toast } = useToast();
  const { user } = useAuth();

  // Fetch roommates
  const { data: roommates } = useApi(
    () => getRoommates(user?.houseId || ''),
    { immediate: !!user?.houseId }
  );

  // Create expense mutation
  const createExpenseMutation = useMutation(createExpense);

  const handleParticipantToggle = (roommateId: string) => {
    setSelectedParticipants(prev => 
      prev.includes(roommateId) 
        ? prev.filter(id => id !== roommateId)
        : [...prev, roommateId]
    );
  };

  // Define preset expense categories
  const expenseCategories = [
    'Electric Bill',
    'Water Bill',
    'Gas Bill',
    'Rent',
    'Grocery',
    'Bread',
    'Snacks',
    'Meat',
    'Other'
  ];

  // Handle description type change
  const handleDescriptionTypeChange = (value: string) => {
    setDescriptionType(value);
    if (value !== 'Other') {
      setDescription(value);
      setCustomDescription('');
    } else {
      setDescription('');
    }
  };

  // Handle custom description change
  const handleCustomDescriptionChange = (value: string) => {
    setCustomDescription(value);
    setDescription(value);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const finalDescription = descriptionType === 'Other' ? customDescription : descriptionType;

    if (!amount || !finalDescription || !payerId || selectedParticipants.length === 0) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    const totalAmount = parseFloat(amount);
    let participants: ExpenseParticipant[];

    if (splitType === 'equal') {
      const perPersonAmount = totalAmount / selectedParticipants.length;
      participants = selectedParticipants.map(id => ({
        roommateId: id,
        amount: perPersonAmount
      }));
    } else {
      participants = selectedParticipants.map(id => ({
        roommateId: id,
        amount: parseFloat(customAmounts[id] || '0')
      }));

      const customTotal = participants.reduce((sum, p) => sum + p.amount, 0);
      if (Math.abs(customTotal - totalAmount) > 0.01) {
        toast({
          title: "Amount mismatch",
          description: `Custom amounts (${customTotal.toFixed(2)}) don't match total (${totalAmount.toFixed(2)}).`,
          variant: "destructive",
        });
        return;
      }
    }

    try {
      const response = await createExpenseMutation.mutate({
        amount: totalAmount,
        description: finalDescription,
        date: new Date().toISOString(),
        payerId,
        participants
      });

      if (response.success) {
        const payerName = response.expense.payerName;
        toast({
          title: "✅ Expense added!",
          description: `${finalDescription} (${totalAmount} ₺) paid by ${payerName} — logged by ${currentUser}`,
        });

        // Reset form
        setAmount('');
        setDescription('');
        setDescriptionType('');
        setCustomDescription('');
        setPayerId('');
        setSelectedParticipants([]);
        setCustomAmounts({});

        onClose();
      }
    } catch (error: any) {
      toast({
        title: "Failed to add expense",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="amount">Amount</Label>
          <Input
            id="amount"
            type="number"
            step="0.01"
            value={amount}
            onChange={(e) => setAmount(e.target.value)}
            placeholder="0.00"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="payer">Paid by</Label>
          <Select value={payerId} onValueChange={setPayerId} required>
            <SelectTrigger>
              <SelectValue placeholder="Select payer" />
            </SelectTrigger>
            <SelectContent>
              {roommates?.roommates?.map((roommate) => (
                <SelectItem key={roommate.id} value={roommate.id}>
                  {roommate.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Select value={descriptionType} onValueChange={handleDescriptionTypeChange} required>
          <SelectTrigger>
            <SelectValue placeholder="What's this expense for?" />
          </SelectTrigger>
          <SelectContent>
            {expenseCategories.map((category) => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {descriptionType === 'Other' && (
          <Input
            id="custom-description"
            value={customDescription}
            onChange={(e) => handleCustomDescriptionChange(e.target.value)}
            placeholder="Enter custom description"
            required
            className="mt-2"
          />
        )}
      </div>

      <div className="space-y-3">
        <Label>Split between</Label>
        <div className="space-y-2">
          {roommates?.roommates?.map((roommate) => (
            <div key={roommate.id} className="flex items-center space-x-2">
              <Checkbox
                id={roommate.id}
                checked={selectedParticipants.includes(roommate.id)}
                onCheckedChange={() => handleParticipantToggle(roommate.id)}
              />
              <Label htmlFor={roommate.id} className="flex-1">
                {roommate.name}
              </Label>
              {splitType === 'custom' && selectedParticipants.includes(roommate.id) && (
                <Input
                  type="number"
                  step="0.01"
                  className="w-20"
                  placeholder="0.00"
                  value={customAmounts[roommate.id] || ''}
                  onChange={(e) => setCustomAmounts(prev => ({
                    ...prev,
                    [roommate.id]: e.target.value
                  }))}
                />
              )}
            </div>
          ))}
        </div>
      </div>

      <div className="flex items-center space-x-4">
        <Button
          type="button"
          variant={splitType === 'equal' ? 'default' : 'outline'}
          onClick={() => setSplitType('equal')}
          size="sm"
        >
          Equal Split
        </Button>
        <Button
          type="button"
          variant={splitType === 'custom' ? 'default' : 'outline'}
          onClick={() => setSplitType('custom')}
          size="sm"
        >
          Custom Split
        </Button>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button type="button" variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button
          type="submit"
          variant="default"
          disabled={createExpenseMutation.loading}
        >
          {createExpenseMutation.loading ? 'Adding...' : 'Add Expense'}
        </Button>
      </div>
    </form>
  );
};
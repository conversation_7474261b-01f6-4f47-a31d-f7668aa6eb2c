import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const Index = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gradient-warm flex items-center justify-center p-4">
      <Card className="w-full max-w-lg shadow-glow bg-card">
        <CardHeader className="text-center">
          <CardTitle className="text-3xl font-bold text-card-foreground mb-2">
            🏠 House Wallet
          </CardTitle>
          <CardDescription className="text-muted-foreground text-lg">
            Split expenses with your roommates, stress-free
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Main Action Buttons */}
          <div className="space-y-3">
            <Button
              onClick={() => navigate('/roommate')}
              variant="warm"
              className="w-full h-14 text-lg font-semibold"
            >
              👥 Roommate Login
            </Button>

            <div className="grid grid-cols-2 gap-3">
              <Button
                onClick={() => navigate('/admin?mode=login')}
                variant="default"
                className="h-12 text-base"
              >
                🔑 Admin Login
              </Button>
              <Button
                onClick={() => navigate('/admin?mode=create')}
                variant="secondary"
                className="h-12 text-base"
              >
                🏠 Create House
              </Button>
            </div>
          </div>

          {/* Features */}
          <div className="pt-4 border-t border-border">
            <div className="space-y-2 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <span>✨</span>
                <span>No passwords for roommates</span>
              </div>
              <div className="flex items-center gap-2">
                <span>🔥</span>
                <span>Smart expense splitting</span>
              </div>
              <div className="flex items-center gap-2">
                <span>📊</span>
                <span>Real-time balance tracking</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Index;

import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { adminLogin } from '@/services/auth';
import { createHouse } from '@/services/houses';

const AdminLogin = () => {
  const [searchParams] = useSearchParams();
  const [mode, setMode] = useState<'login' | 'create'>('login');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [houseName, setHouseName] = useState('');
  const [houseCurrency, setHouseCurrency] = useState('₺');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { setUser } = useAuth();

  // Set initial mode based on URL parameter
  useEffect(() => {
    const modeParam = searchParams.get('mode');
    if (modeParam === 'create') {
      setMode('create');
    }
  }, [searchParams]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await adminLogin(username, password);

      if (response.success) {
        setUser(response.user);
        toast({
          title: "Welcome back!",
          description: `Successfully logged in to ${response.user.houseName}.`,
        });
        navigate('/admin/dashboard');
      }
    } catch (error: any) {
      toast({
        title: "Login failed",
        description: error.message || "Invalid house ID or password.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateHouse = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await createHouse({
        username: username.trim(),
        name: houseName.trim(),
        currency: houseCurrency,
        adminPassword: password.trim()
      });

      if (response.success) {
        toast({
          title: "🏠 House created!",
          description: `${response.house.name} is ready! Username: ${response.house.username}`,
        });

        // Switch to login mode and keep the username
        setMode('login');
        setHouseName('');
        // Keep the username and password so user can immediately login
      }
    } catch (error: any) {
      toast({
        title: "Failed to create house",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-primary flex items-center justify-center p-4">
      <Card className="w-full max-w-md shadow-card bg-card">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold text-primary">
            House Wallet
          </CardTitle>
          <CardDescription className="text-muted-foreground">
            {mode === 'login' ? 'Admin access to manage your house' : 'Create a new house for your roommates'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Mode Toggle */}
          <div className="flex mb-6 bg-muted rounded-lg p-1">
            <Button
              type="button"
              variant={mode === 'login' ? 'default' : 'ghost'}
              className="flex-1 h-8"
              onClick={() => setMode('login')}
            >
              Login
            </Button>
            <Button
              type="button"
              variant={mode === 'create' ? 'default' : 'ghost'}
              className="flex-1 h-8"
              onClick={() => setMode('create')}
            >
              Create House
            </Button>
          </div>

          {mode === 'login' ? (
            <form onSubmit={handleLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  placeholder="Enter username"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter admin password"
                  required
                />
              </div>
              <Button
                type="submit"
                className="w-full shadow-button"
                disabled={loading}
              >
                {loading ? 'Signing in...' : 'Sign In'}
              </Button>
            </form>
          ) : (
            <form onSubmit={handleCreateHouse} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  placeholder="e.g., awesome_house"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="houseName">House Name</Label>
                <Input
                  id="houseName"
                  type="text"
                  value={houseName}
                  onChange={(e) => setHouseName(e.target.value)}
                  placeholder="e.g., The Awesome House"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="currency">Currency</Label>
                <Select value={houseCurrency} onValueChange={setHouseCurrency}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="₺">₺ Turkish Lira</SelectItem>
                    <SelectItem value="$">$ US Dollar</SelectItem>
                    <SelectItem value="€">€ Euro</SelectItem>
                    <SelectItem value="£">£ British Pound</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Admin Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Set admin password (min 4 chars)"
                  required
                  minLength={4}
                />
              </div>
              <Button
                type="submit"
                className="w-full shadow-button"
                disabled={loading}
              >
                {loading ? 'Creating...' : 'Create House'}
              </Button>
            </form>
          )}

          <div className="text-center mt-4">
            <Button
              type="button"
              variant="ghost"
              onClick={() => navigate('/roommate')}
              className="text-sm"
            >
              Continue as roommate instead
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminLogin;
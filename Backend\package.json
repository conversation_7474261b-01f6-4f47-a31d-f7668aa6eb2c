{"name": "house-expenser-backend", "version": "1.0.0", "description": "Backend API for House Expenser application", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "init-db": "node scripts/init-db.js", "clean-db": "node scripts/clean-db.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "sqlite3": "^5.1.6", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-validator": "^7.0.1", "express-rate-limit": "^7.1.5", "morgan": "^1.10.0", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "keywords": ["expense-tracker", "roommate", "house-management", "api"], "author": "House Expenser Team", "license": "MIT"}
import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import { body, validationResult } from 'express-validator';
import { getRow, runQuery, getAllRows, getDatabase } from '../database/init.js';
import { authenticateToken, requireAdmin } from '../middleware/auth.js';

const router = express.Router();

// Validation middleware
const validateCreateExpense = [
  body('amount').isFloat({ min: 0.01 }).withMessage('Amount must be greater than 0'),
  body('description').trim().isLength({ min: 1 }).withMessage('Description is required'),
  body('date').isISO8601().withMessage('Valid date is required'),
  body('payerId').notEmpty().withMessage('Payer ID is required'),
  body('participants').isArray({ min: 1 }).withMessage('At least one participant is required'),
  body('participants.*.roommateId').notEmpty().withMessage('Participant roommate ID is required'),
  body('participants.*.amount').isFloat({ min: 0 }).withMessage('Participant amount must be non-negative')
];

// Get all expenses for a house
router.get('/house/:houseId', authenticateToken, async (req, res) => {
  try {
    const { houseId } = req.params;
    const { limit = 50, offset = 0 } = req.query;

    // Check if user has access to this house
    if (req.user.houseId !== houseId) {
      return res.status(403).json({
        error: 'Access denied to this house'
      });
    }

    // Get expenses with participant details
    const expenses = await getAllRows(`
      SELECT
        e.id,
        e.amount,
        e.description,
        e.date,
        e.payer_id,
        e.logged_by,
        e.logged_at,
        e.house_id,
        e.is_paid,
        e.paid_at,
        e.paid_by,
        p.name as payer_name
      FROM expenses e
      LEFT JOIN roommates p ON e.payer_id = p.id
      WHERE e.house_id = ?
      ORDER BY e.date DESC, e.logged_at DESC
      LIMIT ? OFFSET ?
    `, [houseId, parseInt(limit), parseInt(offset)]);

    // Get participants for each expense
    const expensesWithParticipants = await Promise.all(
      expenses.map(async (expense) => {
        const participants = await getAllRows(`
          SELECT 
            ep.roommate_id,
            ep.amount,
            r.name as roommate_name
          FROM expense_participants ep
          LEFT JOIN roommates r ON ep.roommate_id = r.id
          WHERE ep.expense_id = ?
        `, [expense.id]);

        return {
          id: expense.id,
          amount: expense.amount,
          description: expense.description,
          date: expense.date,
          payerId: expense.payer_id,
          payerName: expense.payer_name,
          participants: participants.map(p => ({
            roommateId: p.roommate_id,
            roommateName: p.roommate_name,
            amount: p.amount
          })),
          loggedBy: expense.logged_by,
          loggedAt: expense.logged_at,
          houseId: expense.house_id,
          isPaid: Boolean(expense.is_paid),
          paidAt: expense.paid_at,
          paidBy: expense.paid_by
        };
      })
    );

    res.json({
      success: true,
      expenses: expensesWithParticipants
    });

  } catch (error) {
    console.error('Get expenses error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Create a new expense
router.post('/', authenticateToken, validateCreateExpense, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const { amount, description, date, payerId, participants } = req.body;
    const houseId = req.user.houseId;
    const loggedBy = req.user.roommateName || 'Admin';

    // Validate that payer exists and belongs to the house
    const payer = await getRow(
      'SELECT id, name FROM roommates WHERE id = ? AND house_id = ?',
      [payerId, houseId]
    );

    if (!payer) {
      return res.status(400).json({
        error: 'Invalid payer: roommate not found in this house'
      });
    }

    // Validate all participants exist and belong to the house
    const participantIds = participants.map(p => p.roommateId);
    const validParticipants = await getAllRows(
      `SELECT id FROM roommates WHERE id IN (${participantIds.map(() => '?').join(',')}) AND house_id = ?`,
      [...participantIds, houseId]
    );

    if (validParticipants.length !== participantIds.length) {
      return res.status(400).json({
        error: 'Invalid participants: some roommates not found in this house'
      });
    }

    // Validate that participant amounts sum to total amount
    const participantTotal = participants.reduce((sum, p) => sum + p.amount, 0);
    if (Math.abs(participantTotal - amount) > 0.01) {
      return res.status(400).json({
        error: `Participant amounts (${participantTotal}) don't match total amount (${amount})`
      });
    }

    const expenseId = uuidv4();

    // Use transaction to ensure data consistency
    const db = await getDatabase();
    
    await new Promise((resolve, reject) => {
      db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        // Insert expense
        db.run(
          'INSERT INTO expenses (id, amount, description, date, payer_id, logged_by, house_id) VALUES (?, ?, ?, ?, ?, ?, ?)',
          [expenseId, amount, description, date, payerId, loggedBy, houseId],
          function(err) {
            if (err) {
              db.run('ROLLBACK');
              db.close();
              return reject(err);
            }

            // Insert participants
            const stmt = db.prepare('INSERT INTO expense_participants (expense_id, roommate_id, amount) VALUES (?, ?, ?)');
            
            let completed = 0;
            participants.forEach((participant) => {
              stmt.run([expenseId, participant.roommateId, participant.amount], (err) => {
                if (err) {
                  db.run('ROLLBACK');
                  db.close();
                  return reject(err);
                }
                
                completed++;
                if (completed === participants.length) {
                  stmt.finalize();
                  db.run('COMMIT');
                  db.close();
                  resolve();
                }
              });
            });
          }
        );
      });
    });

    // Get created expense with details
    const createdExpense = await getRow(`
      SELECT
        e.id,
        e.amount,
        e.description,
        e.date,
        e.payer_id,
        e.logged_by,
        e.logged_at,
        e.house_id,
        e.is_paid,
        e.paid_at,
        e.paid_by,
        p.name as payer_name
      FROM expenses e
      LEFT JOIN roommates p ON e.payer_id = p.id
      WHERE e.id = ?
    `, [expenseId]);

    const expenseParticipants = await getAllRows(`
      SELECT 
        ep.roommate_id,
        ep.amount,
        r.name as roommate_name
      FROM expense_participants ep
      LEFT JOIN roommates r ON ep.roommate_id = r.id
      WHERE ep.expense_id = ?
    `, [expenseId]);

    res.status(201).json({
      success: true,
      expense: {
        id: createdExpense.id,
        amount: createdExpense.amount,
        description: createdExpense.description,
        date: createdExpense.date,
        payerId: createdExpense.payer_id,
        payerName: createdExpense.payer_name,
        participants: expenseParticipants.map(p => ({
          roommateId: p.roommate_id,
          roommateName: p.roommate_name,
          amount: p.amount
        })),
        loggedBy: createdExpense.logged_by,
        loggedAt: createdExpense.logged_at,
        houseId: createdExpense.house_id,
        isPaid: Boolean(createdExpense.is_paid),
        paidAt: createdExpense.paid_at,
        paidBy: createdExpense.paid_by
      }
    });

  } catch (error) {
    console.error('Create expense error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Get expense by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const expense = await getRow(`
      SELECT
        e.id,
        e.amount,
        e.description,
        e.date,
        e.payer_id,
        e.logged_by,
        e.logged_at,
        e.house_id,
        e.is_paid,
        e.paid_at,
        e.paid_by,
        p.name as payer_name
      FROM expenses e
      LEFT JOIN roommates p ON e.payer_id = p.id
      WHERE e.id = ?
    `, [id]);

    if (!expense) {
      return res.status(404).json({
        error: 'Expense not found'
      });
    }

    // Check if user has access to this house
    if (req.user.houseId !== expense.house_id) {
      return res.status(403).json({
        error: 'Access denied'
      });
    }

    const participants = await getAllRows(`
      SELECT 
        ep.roommate_id,
        ep.amount,
        r.name as roommate_name
      FROM expense_participants ep
      LEFT JOIN roommates r ON ep.roommate_id = r.id
      WHERE ep.expense_id = ?
    `, [id]);

    res.json({
      success: true,
      expense: {
        id: expense.id,
        amount: expense.amount,
        description: expense.description,
        date: expense.date,
        payerId: expense.payer_id,
        payerName: expense.payer_name,
        participants: participants.map(p => ({
          roommateId: p.roommate_id,
          roommateName: p.roommate_name,
          amount: p.amount
        })),
        loggedBy: expense.logged_by,
        loggedAt: expense.logged_at,
        houseId: expense.house_id,
        isPaid: Boolean(expense.is_paid),
        paidAt: expense.paid_at,
        paidBy: expense.paid_by
      }
    });

  } catch (error) {
    console.error('Get expense error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Delete expense (admin or creator only)
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const expense = await getRow(
      'SELECT house_id, logged_by FROM expenses WHERE id = ?',
      [id]
    );

    if (!expense) {
      return res.status(404).json({
        error: 'Expense not found'
      });
    }

    // Check if user has access to this house
    if (req.user.houseId !== expense.house_id) {
      return res.status(403).json({
        error: 'Access denied'
      });
    }

    // Check if user is admin or the one who logged the expense
    const currentUser = req.user.roommateName || 'Admin';
    if (!req.user.isAdmin && expense.logged_by !== currentUser) {
      return res.status(403).json({
        error: 'You can only delete expenses you created'
      });
    }

    const result = await runQuery(
      'DELETE FROM expenses WHERE id = ?',
      [id]
    );

    if (result.changes === 0) {
      return res.status(404).json({
        error: 'Expense not found'
      });
    }

    res.json({
      success: true,
      message: 'Expense deleted successfully'
    });

  } catch (error) {
    console.error('Delete expense error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Mark expense as paid (any authenticated user in the house)
router.patch('/:id/paid', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { isPaid } = req.body;

    // Get expense to check house access
    const expense = await getRow(
      'SELECT house_id FROM expenses WHERE id = ?',
      [id]
    );

    if (!expense) {
      return res.status(404).json({
        error: 'Expense not found'
      });
    }

    // Check if user has access to this house
    if (req.user.houseId !== expense.house_id) {
      return res.status(403).json({
        error: 'Access denied'
      });
    }

    const paidBy = req.user.roommateName || 'Admin';
    const paidAt = isPaid ? new Date().toISOString() : null;

    // Update expense payment status
    await runQuery(
      'UPDATE expenses SET is_paid = ?, paid_at = ?, paid_by = ? WHERE id = ?',
      [isPaid, paidAt, isPaid ? paidBy : null, id]
    );

    // Get updated expense with details
    const updatedExpense = await getRow(`
      SELECT
        e.id,
        e.amount,
        e.description,
        e.date,
        e.payer_id,
        e.logged_by,
        e.logged_at,
        e.house_id,
        e.is_paid,
        e.paid_at,
        e.paid_by,
        p.name as payer_name
      FROM expenses e
      LEFT JOIN roommates p ON e.payer_id = p.id
      WHERE e.id = ?
    `, [id]);

    res.json({
      success: true,
      expense: {
        id: updatedExpense.id,
        amount: updatedExpense.amount,
        description: updatedExpense.description,
        date: updatedExpense.date,
        payerId: updatedExpense.payer_id,
        payerName: updatedExpense.payer_name,
        loggedBy: updatedExpense.logged_by,
        loggedAt: updatedExpense.logged_at,
        houseId: updatedExpense.house_id,
        isPaid: Boolean(updatedExpense.is_paid),
        paidAt: updatedExpense.paid_at,
        paidBy: updatedExpense.paid_by
      }
    });

  } catch (error) {
    console.error('Mark expense as paid error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

export default router;

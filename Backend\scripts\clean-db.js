import { getDatabase } from '../src/database/init.js';

async function cleanDatabase() {
  const db = await getDatabase();
  
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      console.log('🧹 Starting database cleanup...');
      
      // Disable foreign key constraints temporarily
      db.run('PRAGMA foreign_keys = OFF', (err) => {
        if (err) {
          console.error('Error disabling foreign keys:', err);
          reject(err);
          return;
        }
        
        console.log('📝 Disabled foreign key constraints');
        
        // Delete all data from tables in the correct order (to avoid foreign key issues)
        const tables = [
          'sessions',
          'expense_participants', 
          'expenses',
          'roommates',
          'houses'
        ];
        
        let completedOperations = 0;
        const totalOperations = tables.length;
        
        function checkComplete() {
          completedOperations++;
          if (completedOperations === totalOperations) {
            // Re-enable foreign key constraints
            db.run('PRAGMA foreign_keys = ON', (err) => {
              if (err) {
                console.error('Error re-enabling foreign keys:', err);
                reject(err);
                return;
              }
              
              console.log('📝 Re-enabled foreign key constraints');
              
              db.close((err) => {
                if (err) {
                  reject(err);
                } else {
                  console.log('✅ Database cleanup completed successfully!');
                  console.log('🎉 All data has been removed. Database is now clean and ready for fresh data.');
                  resolve();
                }
              });
            });
          }
        }
        
        // Delete data from each table
        tables.forEach((table) => {
          console.log(`🗑️  Cleaning table: ${table}`);
          db.run(`DELETE FROM ${table}`, (err) => {
            if (err) {
              console.error(`Error cleaning table ${table}:`, err);
              reject(err);
              return;
            }
            console.log(`✅ Cleaned table: ${table}`);
            checkComplete();
          });
        });
      });
    });
  });
}

async function main() {
  try {
    await cleanDatabase();
    console.log('🎊 Database cleanup completed successfully!');
    console.log('');
    console.log('Your database is now completely clean and ready for fresh data.');
    console.log('You can now create new houses, roommates, and expenses from scratch.');
    process.exit(0);
  } catch (error) {
    console.error('❌ Database cleanup failed:', error);
    process.exit(1);
  }
}

main();
